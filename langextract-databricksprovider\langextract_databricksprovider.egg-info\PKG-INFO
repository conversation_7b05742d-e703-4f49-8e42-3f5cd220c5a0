Metadata-Version: 2.4
Name: langextract-databricksprovider
Version: 0.1.0
Summary: LangExtract provider plugin for DatabricksProvider
License: Apache-2.0
Requires-Python: >=3.10
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: langextract>=1.0.0
Dynamic: license-file

        # LangExtract DatabricksProvider Provider

A provider plugin for LangExtract that supports DatabricksProvider models.

## Installation

```bash
pip install -e .
```

## Supported Model IDs

- `databricksprovider*`: Models matching pattern ^databricksprovider

## Environment Variables

- `DATABRICKSPROVIDER_API_KEY`: API key for authentication

## Usage

```python
import langextract as lx

result = lx.extract(
    text="Your document here",
    model_id="databricksprovider-model",
    prompt_description="Extract entities",
    examples=[...]
)
```

## Development

1. Install in development mode: `pip install -e .`
2. Run tests: `python test_plugin.py`
3. Build package: `python -m build`
4. Publish to PyPI: `twine upload dist/*`

## License

Apache License 2.0
