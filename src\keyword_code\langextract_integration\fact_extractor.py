"""
Fact extraction service using LangExtract.
This module processes LLM analysis responses to extract structured facts.
"""

import langextract as lx
import textwrap
import os
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from ..config import logger


class FactExtractor:
    """Service for extracting structured facts from LLM analysis responses."""

    def __init__(self, model_id: str = "databricks-llama-4-maverick"):
        """
        Initialize the fact extractor.

        Args:
            model_id: The model ID to use for extraction (using Databricks as default)
        """
        self.model_id = model_id

        # Import the custom provider to ensure it's registered
        try:
            import langextract_databricksprovider
            logger.info("Successfully imported Databricks provider for LangExtract")
        except ImportError as e:
            logger.warning(f"Failed to import Databricks provider: {e}")
        except AttributeError as e:
            logger.warning(f"LangExtract providers not available: {e}")
        except Exception as e:
            logger.warning(f"Issue with LangExtract provider setup: {e}")

        # Check for Databricks API key only (workspace policy: Databricks-only)
        if not os.environ.get('DATABRICKS_API_KEY'):
            logger.warning("DATABRICKS_API_KEY not found in environment variables")

    def extract_facts_from_analysis(self, analysis_text: str, sub_prompt: str, context: str = "") -> Optional[Dict[str, Any]]:
        """
        Extract structured facts from an analysis text using LangExtract.

        Args:
            analysis_text: The LLM analysis text to extract facts from
            sub_prompt: The original sub-prompt that generated this analysis
            context: Additional context about the analysis

        Returns:
            Dictionary containing extracted facts or None if extraction fails
        """
        try:
            # Define the extraction prompt
            prompt = textwrap.dedent("""
                Extract key facts and information from the provided analysis text.
                Focus on:
                - Specific data points (numbers, dates, amounts, percentages)
                - Key entities (names, organizations, locations)
                - Important relationships and connections
                - Factual statements and conclusions
                - Any quantitative or qualitative findings

                Extract only factual information that is explicitly stated in the analysis.
                Do not infer or add information that is not directly present.
            """)

            # Create examples to guide the extraction
            examples = [
                lx.data.ExampleData(
                    text=textwrap.dedent("""
                        The loan agreement specifies a total amount of $500 million with an interest rate of 3.5% per annum.
                        The maturity date is set for December 31, 2030. The borrower is ABC Corporation,
                        and the lender is XYZ Bank. The loan includes a refinancing component of $300 million
                        and a greenfield component of $200 million.
                    """),
                    extractions=[
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="$500 million",
                            attributes={"type": "total_loan_amount", "currency": "USD"}
                        ),
                        lx.data.Extraction(
                            extraction_class="interest_rate",
                            extraction_text="3.5% per annum",
                            attributes={"type": "annual_rate", "value": "3.5"}
                        ),
                        lx.data.Extraction(
                            extraction_class="date",
                            extraction_text="December 31, 2030",
                            attributes={"type": "maturity_date", "format": "full_date"}
                        ),
                        lx.data.Extraction(
                            extraction_class="entity",
                            extraction_text="ABC Corporation",
                            attributes={"role": "borrower", "type": "corporation"}
                        ),
                        lx.data.Extraction(
                            extraction_class="entity",
                            extraction_text="XYZ Bank",
                            attributes={"role": "lender", "type": "bank"}
                        ),
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="$300 million",
                            attributes={"type": "refinancing_component", "currency": "USD"}
                        ),
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="$200 million",
                            attributes={"type": "greenfield_component", "currency": "USD"}
                        ),
                    ]
                )
            ]

            # Prepare the input text with context
            input_text = f"""
            Original Question: {sub_prompt}

            Analysis Text:
            {analysis_text}

            Additional Context: {context}
            """
            # Run the extraction

            logger.info(f"Running fact extraction for sub-prompt: {sub_prompt[:50]}...")

            # Databricks-only extraction per workspace policy
            if not os.environ.get('DATABRICKS_API_KEY'):
                logger.error("DATABRICKS_API_KEY not available; cannot perform fact extraction")
                return {
                    "sub_prompt": sub_prompt,
                    "original_analysis": analysis_text,
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": self.model_id,
                        "total_extractions": 0,
                        "error": "Missing DATABRICKS_API_KEY"
                    }
                }

            # Ensure we use a Databricks model ID
            model_to_use = self.model_id if self.model_id.startswith("databricks") else "databricks-llama-4-maverick"

            try:
                result = lx.extract(
                    text_or_documents=input_text,
                    prompt_description=prompt,
                    examples=examples,
                    model_id=model_to_use,
                )
            except Exception as api_error:
                logger.error(f"Databricks extraction failed: {api_error}")
                return {
                    "sub_prompt": sub_prompt,
                    "original_analysis": analysis_text,
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": model_to_use,
                        "total_extractions": 0,
                        "error": str(api_error)
                    }
                }

            # Process the results
            if result and hasattr(result, 'extractions') and result.extractions:
                extracted_facts = {
                    "sub_prompt": sub_prompt,
                    "original_analysis": analysis_text,
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": self.model_id,
                        "total_extractions": len(result.extractions)
                    }
                }

                for extraction in result.extractions:
                    fact = {
                        "category": extraction.extraction_class,
                        "text": extraction.extraction_text,
                        "attributes": extraction.attributes or {}
                    }
                    extracted_facts["extracted_facts"].append(fact)

                logger.info(f"Successfully extracted {len(result.extractions)} facts")
                return extracted_facts
            else:
                logger.warning("No facts extracted from analysis text")
                return None

        except Exception as e:
            logger.error(f"Error during fact extraction: {e}", exc_info=True)
            return None

    def extract_facts_from_multiple_analyses(self, analyses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract facts from multiple analysis results.

        Args:
            analyses: List of analysis dictionaries containing analysis text and metadata

        Returns:
            List of dictionaries containing extracted facts for each analysis
        """
        results = []

        for analysis in analyses:
            try:
                analysis_text = analysis.get("analysis_summary", "")
                sub_prompt = analysis.get("sub_prompt_analyzed", "")
                context = analysis.get("analysis_context", "")

                if not analysis_text:
                    logger.warning(f"No analysis text found for sub-prompt: {sub_prompt}")
                    continue

                extracted_facts = self.extract_facts_from_analysis(
                    analysis_text=analysis_text,
                    sub_prompt=sub_prompt,
                    context=context
                )

                if extracted_facts:
                    # Add original analysis metadata
                    extracted_facts["original_analysis_metadata"] = {
                        "title": analysis.get("title", ""),
                        "supporting_quotes": analysis.get("supporting_quotes", [])
                    }
                    results.append(extracted_facts)

            except Exception as e:
                logger.error(f"Error processing analysis for fact extraction: {e}")
                continue

        logger.info(f"Completed fact extraction for {len(results)} analyses")
        return results

    def extract_facts_from_text(self, text: str, context: str = "") -> Dict[str, Any]:
        """
        Extract facts from a single text (e.g., analysis section).

        Args:
            text: The text to extract facts from
            context: Additional context about the text

        Returns:
            Dictionary containing extracted facts and metadata
        """
        # Databricks-only: require API key
        if not os.environ.get('DATABRICKS_API_KEY'):
            logger.error("DATABRICKS_API_KEY not available; cannot perform fact extraction")
            return {
                "extracted_facts": [],
                "extraction_metadata": {
                    "model_used": self.model_id,
                    "total_extractions": 0,
                    "error": "Missing DATABRICKS_API_KEY",
                    "context": context
                }
            }

        try:
            import langextract as lx

            # Create examples that demonstrate fact-definition pattern extraction
            # Focus on teaching LangExtract to identify facts and their definitions naturally
            examples = [
                lx.data.ExampleData(
                    text="The currency of the loan is specified as Dollars, which is defined as the lawful currency of the United States of America.",
                    extractions=[
                        lx.data.Extraction(
                            extraction_class="fact_definition",
                            extraction_text="Dollars",
                            attributes={
                                "fact": "Dollars",
                                "definition": "lawful currency of the United States of America",
                                "context": "currency"
                            }
                        ),
                    ]
                ),
                lx.data.ExampleData(
                    text="The total loan amount is US$30,000,000, comprising an A loan of US$15,000,000 and a B loan of US$15,000,000.",
                    extractions=[
                        lx.data.Extraction(
                            extraction_class="fact_definition",
                            extraction_text="total loan amount",
                            attributes={
                                "fact": "total loan amount",
                                "definition": "US$30,000,000",
                                "context": "financial_amount"
                            }
                        ),
                        lx.data.Extraction(
                            extraction_class="fact_definition",
                            extraction_text="A loan",
                            attributes={
                                "fact": "A loan",
                                "definition": "US$15,000,000",
                                "context": "financial_amount"
                            }
                        ),
                        lx.data.Extraction(
                            extraction_class="fact_definition",
                            extraction_text="B loan",
                            attributes={
                                "fact": "B loan",
                                "definition": "US$15,000,000",
                                "context": "financial_amount"
                            }
                        ),
                    ]
                ),
                lx.data.ExampleData(
                    text="The Relevant Spread is 4.3% per annum prior to the Security Perfection Date and 4.2% per annum on and from the Security Perfection Date.",
                    extractions=[
                        lx.data.Extraction(
                            extraction_class="fact_definition",
                            extraction_text="Relevant Spread prior to Security Perfection Date",
                            attributes={
                                "fact": "Relevant Spread prior to Security Perfection Date",
                                "definition": "4.3% per annum",
                                "context": "interest_rate"
                            }
                        ),
                        lx.data.Extraction(
                            extraction_class="fact_definition",
                            extraction_text="Relevant Spread on and from Security Perfection Date",
                            attributes={
                                "fact": "Relevant Spread on and from Security Perfection Date",
                                "definition": "4.2% per annum",
                                "context": "interest_rate"
                            }
                        ),
                    ]
                ),
                lx.data.ExampleData(
                    text="Business Day means a SOFR Banking Day for determining Interest Rate purposes.",
                    extractions=[
                        lx.data.Extraction(
                            extraction_class="fact_definition",
                            extraction_text="Business Day",
                            attributes={
                                "fact": "Business Day",
                                "definition": "SOFR Banking Day",
                                "context": "term_definition"
                            }
                        ),
                    ]
                )
            ]

            # Databricks-only extraction per workspace policy
            model_to_use = self.model_id if self.model_id.startswith("databricks") else "databricks-llama-4-maverick"

            # Create a focused prompt that teaches LangExtract the fact-definition pattern
            prompt_description = textwrap.dedent("""
                Extract facts and their definitions from legal/financial text.

                For each important piece of information, identify:
                1. The FACT (key term, concept, or entity)
                2. The DEFINITION (what it means, its value, or how it's defined)
                3. The CONTEXT (what type of information this is)

                Focus on extracting pairs where one element defines, explains, or gives value to another.
                Examples of fact-definition relationships:
                - Terms and their meanings (Business Day = SOFR Banking Day)
                - Amounts and their values (A loan = US$15,000,000)
                - Rates and their percentages (Relevant Spread = 4.3%)
                - Entities and their descriptions (Currency = lawful currency of United States)

                Use the 'fact_definition' extraction class and include 'fact', 'definition', and 'context' attributes.
            """).strip()

            try:
                result = lx.extract(
                    text_or_documents=text,  # Use the actual text, not a prompt wrapper
                    prompt_description=prompt_description,
                    examples=examples,
                    model_id=model_to_use,
                    fence_output=False,  # Try without fencing for Databricks
                    use_schema_constraints=False,  # Disable schema constraints that might cause JSON issues
                )
            except Exception as e:
                logger.error(f"Databricks extraction failed: {e}")
                return {
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": model_to_use,
                        "total_extractions": 0,
                        "error": str(e),
                        "context": context
                    }
                }

            # Process and return results (normalize to our schema)
            facts_out = []
            if hasattr(result, 'extractions') and result.extractions:
                for ext in result.extractions:
                    facts_out.append({
                        "category": getattr(ext, "extraction_class", None),
                        "text": getattr(ext, "extraction_text", None),
                        "attributes": getattr(ext, "attributes", {}) or {}
                    })
            else:
                logger.warning("No facts extracted from text")

            return {
                "extracted_facts": facts_out,
                "extraction_metadata": {
                    "model_used": model_to_use,
                    "total_extractions": len(facts_out),
                    "context": context
                }
            }

        except Exception as e:
            logger.error(f"Error extracting facts from text: {e}", exc_info=True)
            return {
                "extracted_facts": [],
                "extraction_metadata": {
                    "model_used": self.model_id,
                    "total_extractions": 0,
                    "error": str(e),
                    "context": context
                }
            }

    # -------------------- Flexible Fact/Definition Extraction --------------------
    def _extract_fact_definition_pairs(self, item: Dict[str, Any]) -> List[Tuple[str, str]]:
        """
        Extract fact-definition pairs from LangExtract items using native attributes.
        Leverages LangExtract's natural tagging instead of hard-coded normalization.
        """
        pairs: List[Tuple[str, str]] = []
        text = (item or {}).get("text") or (item or {}).get("extraction_text") or ""
        category = (item or {}).get("category") or (item or {}).get("extraction_class") or ""
        attrs = (item or {}).get("attributes") or {}

        logger.debug(f"Processing item: text='{text}', category='{category}', attrs={attrs}")

        # Method 1: Direct fact-definition attributes (preferred approach)
        fact = attrs.get("fact")
        definition = attrs.get("definition")
        if fact and definition:
            pairs.append((str(fact).strip(), str(definition).strip()))
            logger.debug(f"Found direct fact-definition: {fact} -> {definition}")
            return pairs

        # Method 2: LangExtract identified this as fact_definition class
        if category == "fact_definition":
            # Use the extraction text as fact, look for definition in attributes
            if definition:
                pairs.append((text.strip(), str(definition).strip()))
            elif attrs.get("value"):
                pairs.append((text.strip(), str(attrs["value"]).strip()))
            else:
                # Fallback: use text as both fact and definition if no separate definition
                pairs.append((text.strip(), text.strip()))
            return pairs

        # Method 3: Traditional attribute-based extraction with flexible mapping
        # Look for any attribute that could be a definition
        definition_keys = ["definition", "meaning", "value", "amount", "rate", "percentage", "description"]
        for def_key in definition_keys:
            if def_key in attrs and attrs[def_key]:
                # Use text as fact, attribute as definition
                pairs.append((text.strip(), str(attrs[def_key]).strip()))
                logger.debug(f"Found {def_key} mapping: {text} -> {attrs[def_key]}")
                return pairs

        # Method 4: Use category and text for context-aware extraction
        if category and text.strip():
            # For financial amounts, rates, etc., the text often IS the definition
            if category.lower() in {"financial_amount", "percentage", "rate", "currency", "date"}:
                # Try to infer a meaningful fact name from attributes or context
                fact_name = attrs.get("type") or attrs.get("name") or category
                pairs.append((str(fact_name).strip(), text.strip()))
            else:
                # For other categories, text might be the fact, category the definition
                pairs.append((text.strip(), category.strip()))

            logger.debug(f"Category-based mapping: {text} -> {category}")
            return pairs

        # Method 5: Fallback - if we have meaningful text, create a basic pair
        if text.strip() and len(text.strip()) > 1:
            # Use text as fact, try to find any meaningful attribute as definition
            definition_value = None
            for key, value in attrs.items():
                if value and str(value).strip() and key not in ["extraction_class", "category"]:
                    definition_value = str(value).strip()
                    break

            if definition_value:
                pairs.append((text.strip(), definition_value))
            else:
                # Last resort: use text as both fact and definition
                pairs.append((text.strip(), text.strip()))

            logger.debug(f"Fallback mapping: {text} -> {definition_value or text}")

        return pairs

    def extract_fact_definitions_from_text(self, text: str, context: str = "", *, section_name: str = "", filename: str = "") -> List[Dict[str, str]]:
        """High-level helper: run extraction then normalize to Fact/Definition rows."""
        rows: List[Dict[str, str]] = []

        # Debug logging
        logger.info(f"Starting fact extraction for section: {section_name}")
        logger.debug(f"Text length: {len(text)} characters")

        try:
            res = self.extract_facts_from_text(text=text, context=context) or {}
            logger.info(f"Raw extraction result: {res}")

            items = res.get("extracted_facts", [])
            logger.info(f"Found {len(items)} raw extracted items")

            # Debug: log all raw items
            for i, item in enumerate(items):
                logger.debug(f"Raw item {i}: {item}")

            if not items:
                logger.warning("No items extracted from text")
                return rows

            seen: set[Tuple[str, str]] = set()
            for i, it in enumerate(items):
                logger.debug(f"Processing item {i}: {it}")
                try:
                    pairs = self._extract_fact_definition_pairs(it)
                    logger.debug(f"Extracted {len(pairs)} fact-definition pairs: {pairs}")

                    for fact, definition in pairs:
                        key = (fact.strip(), definition.strip())
                        if key in seen:
                            logger.debug(f"Skipping duplicate: {key}")
                            continue
                        seen.add(key)
                        rows.append({
                            "Filename": filename or "",
                            "Section": section_name or "",
                            "Fact": fact.strip(),
                            "Definition": definition.strip(),
                        })
                        logger.debug(f"Added fact: {fact.strip()} -> {definition.strip()}")
                except Exception as e:
                    logger.error(f"Error extracting fact-definition pairs from item {i}: {e}")
                    continue

            logger.info(f"Final result: {len(rows)} fact definitions extracted")
            return rows

        except Exception as e:
            logger.error(f"Error in extract_fact_definitions_from_text: {e}", exc_info=True)
            return rows

    def extract_fact_definitions_for_results(self, results_with_real_analysis: List[Tuple[Dict[str, Any], Dict[str, Any]]]) -> List[Dict[str, str]]:
        """Aggregate Fact/Definition rows across all files/sections from UI results."""
        aggregated: List[Dict[str, str]] = []
        seen_global: set[Tuple[str, str, str]] = set()  # (Filename, Section, Fact)
        for res, ai in results_with_real_analysis:
            filename = res.get("filename", "Unknown File")
            sections = (ai or {}).get("analysis_sections", {}) or {}
            for section_key, section_data in sections.items():
                text = (section_data or {}).get("Analysis", "")
                if not text:
                    continue
                rows = self.extract_fact_definitions_from_text(
                    text=text,
                    context=f"Section: {section_key} in {filename}",
                    section_name=section_key,
                    filename=filename,
                )
                for row in rows:
                    k = (row["Filename"], row["Section"], row["Fact"])
                    if k in seen_global:
                        continue
                    seen_global.add(k)
                    aggregated.append(row)
        return aggregated


