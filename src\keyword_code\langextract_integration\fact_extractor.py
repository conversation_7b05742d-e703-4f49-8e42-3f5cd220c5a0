"""
Fact extraction service using LangExtract.
This module processes LLM analysis responses to extract structured facts.
"""

import langextract as lx
import textwrap
import os
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from ..config import logger


class FactExtractor:
    """Service for extracting structured facts from LLM analysis responses."""

    def __init__(self, model_id: str = "databricks-llama-4-maverick"):
        """
        Initialize the fact extractor.

        Args:
            model_id: The model ID to use for extraction (using Databricks as default)
        """
        self.model_id = model_id

        # Import the custom provider to ensure it's registered
        try:
            import langextract_databricksprovider
            logger.info("Successfully imported Databricks provider for LangExtract")
        except ImportError as e:
            logger.warning(f"Failed to import Databricks provider: {e}")
        except AttributeError as e:
            logger.warning(f"LangExtract providers not available: {e}")
        except Exception as e:
            logger.warning(f"Issue with LangExtract provider setup: {e}")

        # Check for Databricks API key only (workspace policy: Databricks-only)
        if not os.environ.get('DATABRICKS_API_KEY'):
            logger.warning("DATABRICKS_API_KEY not found in environment variables")

    def extract_facts_from_analysis(self, analysis_text: str, sub_prompt: str, context: str = "") -> Optional[Dict[str, Any]]:
        """
        Extract structured facts from an analysis text using LangExtract.

        Args:
            analysis_text: The LLM analysis text to extract facts from
            sub_prompt: The original sub-prompt that generated this analysis
            context: Additional context about the analysis

        Returns:
            Dictionary containing extracted facts or None if extraction fails
        """
        try:
            # Define the extraction prompt
            prompt = textwrap.dedent("""
                Extract key facts and information from the provided analysis text.
                Focus on:
                - Specific data points (numbers, dates, amounts, percentages)
                - Key entities (names, organizations, locations)
                - Important relationships and connections
                - Factual statements and conclusions
                - Any quantitative or qualitative findings

                Extract only factual information that is explicitly stated in the analysis.
                Do not infer or add information that is not directly present.
            """)

            # Create examples to guide the extraction
            examples = [
                lx.data.ExampleData(
                    text=textwrap.dedent("""
                        The loan agreement specifies a total amount of $500 million with an interest rate of 3.5% per annum.
                        The maturity date is set for December 31, 2030. The borrower is ABC Corporation,
                        and the lender is XYZ Bank. The loan includes a refinancing component of $300 million
                        and a greenfield component of $200 million.
                    """),
                    extractions=[
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="$500 million",
                            attributes={"type": "total_loan_amount", "currency": "USD"}
                        ),
                        lx.data.Extraction(
                            extraction_class="interest_rate",
                            extraction_text="3.5% per annum",
                            attributes={"type": "annual_rate", "value": "3.5"}
                        ),
                        lx.data.Extraction(
                            extraction_class="date",
                            extraction_text="December 31, 2030",
                            attributes={"type": "maturity_date", "format": "full_date"}
                        ),
                        lx.data.Extraction(
                            extraction_class="entity",
                            extraction_text="ABC Corporation",
                            attributes={"role": "borrower", "type": "corporation"}
                        ),
                        lx.data.Extraction(
                            extraction_class="entity",
                            extraction_text="XYZ Bank",
                            attributes={"role": "lender", "type": "bank"}
                        ),
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="$300 million",
                            attributes={"type": "refinancing_component", "currency": "USD"}
                        ),
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="$200 million",
                            attributes={"type": "greenfield_component", "currency": "USD"}
                        ),
                    ]
                )
            ]

            # Prepare the input text with context
            input_text = f"""
            Original Question: {sub_prompt}

            Analysis Text:
            {analysis_text}

            Additional Context: {context}
            """

            # Run the extraction
            logger.info(f"Running fact extraction for sub-prompt: {sub_prompt[:50]}...")

            # Databricks-only extraction per workspace policy
            if not os.environ.get('DATABRICKS_API_KEY'):
                logger.error("DATABRICKS_API_KEY not available; cannot perform fact extraction")
                return {
                    "sub_prompt": sub_prompt,
                    "original_analysis": analysis_text,
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": self.model_id,
                        "total_extractions": 0,
                        "error": "Missing DATABRICKS_API_KEY"
                    }
                }

            # Ensure we use a Databricks model ID
            model_to_use = self.model_id if self.model_id.startswith("databricks") else "databricks-llama-4-maverick"

            try:
                result = lx.extract(
                    text_or_documents=input_text,
                    prompt_description=prompt,
                    examples=examples,
                    model_id=model_to_use,
                )
            except Exception as api_error:
                logger.error(f"Databricks extraction failed: {api_error}")
                return {
                    "sub_prompt": sub_prompt,
                    "original_analysis": analysis_text,
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": model_to_use,
                        "total_extractions": 0,
                        "error": str(api_error)
                    }
                }

            # Process the results
            if result and hasattr(result, 'extractions') and result.extractions:
                extracted_facts = {
                    "sub_prompt": sub_prompt,
                    "original_analysis": analysis_text,
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": self.model_id,
                        "total_extractions": len(result.extractions)
                    }
                }

                for extraction in result.extractions:
                    fact = {
                        "category": extraction.extraction_class,
                        "text": extraction.extraction_text,
                        "attributes": extraction.attributes or {}
                    }
                    extracted_facts["extracted_facts"].append(fact)

                logger.info(f"Successfully extracted {len(result.extractions)} facts")
                return extracted_facts
            else:
                logger.warning("No facts extracted from analysis text")
                return None

        except Exception as e:
            logger.error(f"Error during fact extraction: {e}", exc_info=True)
            return None

    def extract_facts_from_multiple_analyses(self, analyses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract facts from multiple analysis results.

        Args:
            analyses: List of analysis dictionaries containing analysis text and metadata

        Returns:
            List of dictionaries containing extracted facts for each analysis
        """
        results = []

        for analysis in analyses:
            try:
                analysis_text = analysis.get("analysis_summary", "")
                sub_prompt = analysis.get("sub_prompt_analyzed", "")
                context = analysis.get("analysis_context", "")

                if not analysis_text:
                    logger.warning(f"No analysis text found for sub-prompt: {sub_prompt}")
                    continue

                extracted_facts = self.extract_facts_from_analysis(
                    analysis_text=analysis_text,
                    sub_prompt=sub_prompt,
                    context=context
                )

                if extracted_facts:
                    # Add original analysis metadata
                    extracted_facts["original_analysis_metadata"] = {
                        "title": analysis.get("title", ""),
                        "supporting_quotes": analysis.get("supporting_quotes", [])
                    }
                    results.append(extracted_facts)

            except Exception as e:
                logger.error(f"Error processing analysis for fact extraction: {e}")
                continue

        logger.info(f"Completed fact extraction for {len(results)} analyses")
        return results

    def extract_facts_from_text(self, text: str, context: str = "") -> Dict[str, Any]:
        """
        Extract facts from a single text (e.g., analysis section).

        Args:
            text: The text to extract facts from
            context: Additional context about the text

        Returns:
            Dictionary containing extracted facts and metadata
        """
        # Databricks-only: require API key
        if not os.environ.get('DATABRICKS_API_KEY'):
            logger.error("DATABRICKS_API_KEY not available; cannot perform fact extraction")
            return {
                "extracted_facts": [],
                "extraction_metadata": {
                    "model_used": self.model_id,
                    "total_extractions": 0,
                    "error": "Missing DATABRICKS_API_KEY",
                    "context": context
                }
            }

        try:
            import langextract as lx

            # Create better examples for legal/financial fact extraction
            examples = [
                lx.data.ExampleData(
                    text="The currency of the loan is specified as Dollars, which is defined as the lawful currency of the United States of America.",
                    extractions=[
                        lx.data.Extraction(
                            extraction_class="currency",
                            extraction_text="Dollars",
                            attributes={"definition": "lawful currency of the United States of America"}
                        ),
                    ]
                ),
                lx.data.ExampleData(
                    text="The total loan amount is specified as US$30,000,000. It is split between an A loan of US$15,000,000 and a B loan of US$15,000,000.",
                    extractions=[
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="US$30,000,000",
                            attributes={"type": "total_loan_amount"}
                        ),
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="US$15,000,000",
                            attributes={"type": "a_loan_amount"}
                        ),
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="US$15,000,000",
                            attributes={"type": "b_loan_amount"}
                        ),
                    ]
                ),
                lx.data.ExampleData(
                    text="The Relevant Spread is defined as 4.3% per annum before the Security Perfection Date and 4.2% per annum on and from the Security Perfection Date.",
                    extractions=[
                        lx.data.Extraction(
                            extraction_class="percentage",
                            extraction_text="4.3%",
                            attributes={"condition": "before the Security Perfection Date"}
                        ),
                        lx.data.Extraction(
                            extraction_class="percentage",
                            extraction_text="4.2%",
                            attributes={"condition": "on and from the Security Perfection Date"}
                        ),
                    ]
                ),
                lx.data.ExampleData(
                    text="A Business Day is defined as (a) for determining the Interest Rate, a SOFR Banking Day; and (b) for all other purposes, a day that is a SOFR Banking Day.",
                    extractions=[
                        lx.data.Extraction(
                            extraction_class="definition",
                            extraction_text="SOFR Banking Day",
                            attributes={"term": "Business Day", "definition": "SOFR Banking Day"}
                        ),
                    ]
                )
            ]

            # Databricks-only extraction per workspace policy
            model_to_use = self.model_id if self.model_id.startswith("databricks") else "databricks-llama-4-maverick"

            # Create a more focused prompt description
            prompt_description = textwrap.dedent("""
                Extract key facts and definitions from legal/financial text.
                Focus on:
                - Currency definitions and amounts
                - Loan amounts (total, A loan, B loan)
                - Interest rates and spreads with conditions
                - Term definitions (Business Day, etc.)
                - Financial entities and their roles

                Extract the exact text and provide relevant attributes like type, condition, or definition.
            """).strip()

            try:
                result = lx.extract(
                    text_or_documents=text,  # Use the actual text, not a prompt wrapper
                    prompt_description=prompt_description,
                    examples=examples,
                    model_id=model_to_use,
                    fence_output=False,  # Try without fencing for Databricks
                    use_schema_constraints=False,  # Disable schema constraints that might cause JSON issues
                )
            except Exception as e:
                logger.error(f"Databricks extraction failed: {e}")
                return {
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": model_to_use,
                        "total_extractions": 0,
                        "error": str(e),
                        "context": context
                    }
                }

            # Process and return results (normalize to our schema)
            facts_out = []
            if hasattr(result, 'extractions') and result.extractions:
                for ext in result.extractions:
                    facts_out.append({
                        "category": getattr(ext, "extraction_class", None),
                        "text": getattr(ext, "extraction_text", None),
                        "attributes": getattr(ext, "attributes", {}) or {}
                    })
            else:
                logger.warning("No facts extracted from text")

            return {
                "extracted_facts": facts_out,
                "extraction_metadata": {
                    "model_used": model_to_use,
                    "total_extractions": len(facts_out),
                    "context": context
                }
            }

        except Exception as e:
            logger.error(f"Error extracting facts from text: {e}", exc_info=True)
            return {
                "extracted_facts": [],
                "extraction_metadata": {
                    "model_used": self.model_id,
                    "total_extractions": 0,
                    "error": str(e),
                    "context": context
                }
            }

    # -------------------- Normalization to Fact/Definition --------------------
    def _normalize_fact_item(self, item: Dict[str, Any], section_text: str) -> List[Tuple[str, str]]:
        """
        Convert a single LangExtract item into standardized (Fact, Definition) pairs.
        Heuristics align with requested patterns: currency definitions, loan amounts,
        spread by condition, and term definitions like Business Day.
        """
        pairs: List[Tuple[str, str]] = []
        text = (item or {}).get("text") or (item or {}).get("extraction_text") or ""
        category = (item or {}).get("category") or (item or {}).get("extraction_class") or ""
        attrs = (item or {}).get("attributes") or {}

        # 1) Direct definition attribute present
        definition_attr = None
        for key in ("definition", "defined_as", "meaning"):
            if key in attrs and attrs[key]:
                definition_attr = str(attrs[key])
                break
        if definition_attr:
            term = attrs.get("term") or attrs.get("name") or text
            if term and definition_attr:
                pairs.append((str(term), definition_attr))
                return pairs

        # 2) Loan amounts (total / A / B)
        type_key = (attrs.get("type") or attrs.get("kind") or "").lower()
        normalized_amount = text
        if type_key in {"total_loan_amount", "total_amount", "loan_total"}:
            pairs.append(("total loan amount", normalized_amount))
        elif type_key in {"a_loan_amount", "loan_a_amount", "a_loan"}:
            pairs.append(("A Loan amount", normalized_amount))
        elif type_key in {"b_loan_amount", "loan_b_amount", "b_loan"}:
            pairs.append(("B Loan amount", normalized_amount))
        elif category.lower() in {"financial_amount", "amount"} and re.search(r"\b[aA]\s*loan\b", section_text):
            # Fallback heuristic: infer A/B from nearby section text signals if present
            if re.search(r"\bA\s*loan\b", section_text, re.IGNORECASE):
                pairs.append(("A Loan amount", normalized_amount))
            if re.search(r"\bB\s*loan\b", section_text, re.IGNORECASE):
                pairs.append(("B Loan amount", normalized_amount))

        # 3) Relevant Spread by condition
        if ("relevant spread" in section_text.lower()) or (category.lower() in {"percentage", "rate", "spread", "interest_rate"}):
            cond = (attrs.get("condition") or attrs.get("when") or "").strip()
            if cond:
                # Normalize phrasing
                cond_norm = cond
                cond_norm = re.sub(r"^prior to\b", "before", cond_norm, flags=re.IGNORECASE)
                cond_norm = re.sub(r"\bon and from\b", "on and from", cond_norm, flags=re.IGNORECASE)
                # Extract numeric percent if present
                m = re.search(r"(\d+(?:\.\d+)?)\s*%", text)
                value = f"{m.group(1)}%" if m else text
                # Prefer 'Spread' label if Relevant Spread mentioned in section
                label_prefix = "Spread" if "relevant spread" in section_text.lower() else "Rate"
                pairs.append((f"{label_prefix} {cond_norm}".strip(), value))

        # 4) Business Day definition -> SOFR Banking Day
        if re.search(r"\bBusiness Day\b", section_text) and re.search(r"\bSOFR Banking Day\b", section_text):
            # If this item is the SOFR Banking Day mention, bind it to Business Day
            if re.search(r"\bSOFR Banking Day\b", text):
                pairs.append(("Business Day", "SOFR Banking Day"))

        # Generic pattern: "X is defined as Y"
        if not pairs:
            m = re.search(r"\b([A-Z][A-Za-z0-9 _-]{2,}?)\s+is\s+defined\s+as\s+(.+?)\.", section_text)
            if m:
                term, defn = m.group(1).strip(), m.group(2).strip()
                # Avoid huge trailing qualifiers; keep concise first clause if present
                defn = defn.split(";")[0]
                pairs.append((term, defn))

        # Fallback: if we have text but no pairs, try to extract something useful
        if not pairs and text.strip():
            # If it looks like a financial amount, use it as-is
            if re.search(r"[\$€£¥]\s*[\d,]+(?:\.\d+)?(?:\s*(?:million|billion|thousand))?", text, re.IGNORECASE):
                pairs.append((text.strip(), text.strip()))
            # If it looks like a percentage
            elif re.search(r"\d+(?:\.\d+)?\s*%", text):
                pairs.append((text.strip(), text.strip()))
            # If it's a term with definition in attributes
            elif attrs.get("definition"):
                pairs.append((text.strip(), str(attrs["definition"])))
            # If it's a simple entity or term
            elif len(text.strip()) > 2 and len(text.strip()) < 100:
                pairs.append((text.strip(), category or "general"))

        # 5) Currency simple mapping if attributes indicate currency
        if (not pairs) and (category.lower() in {"currency", "entity"} or "currency" in attrs):
            currency_val = attrs.get("currency") or text
            # Try to find lawful currency definition in section text
            m = re.search(r"lawful currency of the United States of America", section_text, flags=re.IGNORECASE)
            if m:
                pairs.append((str(currency_val), "lawful currency of the United States of America"))

        return pairs

    def extract_fact_definitions_from_text(self, text: str, context: str = "", *, section_name: str = "", filename: str = "") -> List[Dict[str, str]]:
        """High-level helper: run extraction then normalize to Fact/Definition rows."""
        rows: List[Dict[str, str]] = []

        # Debug logging
        logger.info(f"Starting fact extraction for section: {section_name}")
        logger.debug(f"Text length: {len(text)} characters")

        try:
            res = self.extract_facts_from_text(text=text, context=context) or {}
            logger.info(f"Raw extraction result: {res}")

            items = res.get("extracted_facts", [])
            logger.info(f"Found {len(items)} raw extracted items")

            if not items:
                logger.warning("No items extracted from text")
                return rows

            seen: set[Tuple[str, str]] = set()
            for i, it in enumerate(items):
                logger.debug(f"Processing item {i}: {it}")
                try:
                    pairs = self._normalize_fact_item(it, section_text=text)
                    logger.debug(f"Normalized to {len(pairs)} pairs: {pairs}")

                    for fact, definition in pairs:
                        key = (fact.strip(), definition.strip())
                        if key in seen:
                            logger.debug(f"Skipping duplicate: {key}")
                            continue
                        seen.add(key)
                        rows.append({
                            "Filename": filename or "",
                            "Section": section_name or "",
                            "Fact": fact.strip(),
                            "Definition": definition.strip(),
                        })
                        logger.debug(f"Added fact: {fact.strip()} -> {definition.strip()}")
                except Exception as e:
                    logger.error(f"Error normalizing item {i}: {e}")
                    continue

            logger.info(f"Final result: {len(rows)} fact definitions extracted")
            return rows

        except Exception as e:
            logger.error(f"Error in extract_fact_definitions_from_text: {e}", exc_info=True)
            return rows

    def extract_fact_definitions_for_results(self, results_with_real_analysis: List[Tuple[Dict[str, Any], Dict[str, Any]]]) -> List[Dict[str, str]]:
        """Aggregate Fact/Definition rows across all files/sections from UI results."""
        aggregated: List[Dict[str, str]] = []
        seen_global: set[Tuple[str, str, str]] = set()  # (Filename, Section, Fact)
        for res, ai in results_with_real_analysis:
            filename = res.get("filename", "Unknown File")
            sections = (ai or {}).get("analysis_sections", {}) or {}
            for section_key, section_data in sections.items():
                text = (section_data or {}).get("Analysis", "")
                if not text:
                    continue
                rows = self.extract_fact_definitions_from_text(
                    text=text,
                    context=f"Section: {section_key} in {filename}",
                    section_name=section_key,
                    filename=filename,
                )
                for row in rows:
                    k = (row["Filename"], row["Section"], row["Fact"])
                    if k in seen_global:
                        continue
                    seen_global.add(k)
                    aggregated.append(row)
        return aggregated


