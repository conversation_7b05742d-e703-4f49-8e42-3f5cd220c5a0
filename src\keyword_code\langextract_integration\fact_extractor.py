"""
Fact extraction service using LangExtract.
This module processes LLM analysis responses to extract structured facts.
"""

import langextract as lx
import textwrap
import os
from typing import Dict, List, Any, Optional
from ..config import logger


class FactExtractor:
    """Service for extracting structured facts from LLM analysis responses."""

    def __init__(self, model_id: str = "databricks-llama-4-maverick"):
        """
        Initialize the fact extractor.

        Args:
            model_id: The model ID to use for extraction (using Databricks as default)
        """
        self.model_id = model_id

        # Import the custom provider to ensure it's registered
        try:
            import langextract_databricksprovider
            logger.info("Successfully imported Databricks provider for LangExtract")
        except ImportError as e:
            logger.warning(f"Failed to import Databricks provider: {e}")
        except AttributeError as e:
            logger.warning(f"LangExtract providers not available: {e}")
        except Exception as e:
            logger.warning(f"Issue with LangExtract provider setup: {e}")

        # Check for Databricks API key only (workspace policy: Databricks-only)
        if not os.environ.get('DATABRICKS_API_KEY'):
            logger.warning("DATABRICKS_API_KEY not found in environment variables")
    
    def extract_facts_from_analysis(self, analysis_text: str, sub_prompt: str, context: str = "") -> Optional[Dict[str, Any]]:
        """
        Extract structured facts from an analysis text using LangExtract.
        
        Args:
            analysis_text: The LLM analysis text to extract facts from
            sub_prompt: The original sub-prompt that generated this analysis
            context: Additional context about the analysis
            
        Returns:
            Dictionary containing extracted facts or None if extraction fails
        """
        try:
            # Define the extraction prompt
            prompt = textwrap.dedent("""
                Extract key facts and information from the provided analysis text.
                Focus on:
                - Specific data points (numbers, dates, amounts, percentages)
                - Key entities (names, organizations, locations)
                - Important relationships and connections
                - Factual statements and conclusions
                - Any quantitative or qualitative findings
                
                Extract only factual information that is explicitly stated in the analysis.
                Do not infer or add information that is not directly present.
            """)
            
            # Create examples to guide the extraction
            examples = [
                lx.data.ExampleData(
                    text=textwrap.dedent("""
                        The loan agreement specifies a total amount of $500 million with an interest rate of 3.5% per annum. 
                        The maturity date is set for December 31, 2030. The borrower is ABC Corporation, 
                        and the lender is XYZ Bank. The loan includes a refinancing component of $300 million 
                        and a greenfield component of $200 million.
                    """),
                    extractions=[
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="$500 million",
                            attributes={"type": "total_loan_amount", "currency": "USD"}
                        ),
                        lx.data.Extraction(
                            extraction_class="interest_rate",
                            extraction_text="3.5% per annum",
                            attributes={"type": "annual_rate", "value": "3.5"}
                        ),
                        lx.data.Extraction(
                            extraction_class="date",
                            extraction_text="December 31, 2030",
                            attributes={"type": "maturity_date", "format": "full_date"}
                        ),
                        lx.data.Extraction(
                            extraction_class="entity",
                            extraction_text="ABC Corporation",
                            attributes={"role": "borrower", "type": "corporation"}
                        ),
                        lx.data.Extraction(
                            extraction_class="entity",
                            extraction_text="XYZ Bank",
                            attributes={"role": "lender", "type": "bank"}
                        ),
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="$300 million",
                            attributes={"type": "refinancing_component", "currency": "USD"}
                        ),
                        lx.data.Extraction(
                            extraction_class="financial_amount",
                            extraction_text="$200 million",
                            attributes={"type": "greenfield_component", "currency": "USD"}
                        ),
                    ]
                )
            ]
            
            # Prepare the input text with context
            input_text = f"""
            Original Question: {sub_prompt}
            
            Analysis Text:
            {analysis_text}
            
            Additional Context: {context}
            """
            
            # Run the extraction
            logger.info(f"Running fact extraction for sub-prompt: {sub_prompt[:50]}...")

            # Databricks-only extraction per workspace policy
            if not os.environ.get('DATABRICKS_API_KEY'):
                logger.error("DATABRICKS_API_KEY not available; cannot perform fact extraction")
                return {
                    "sub_prompt": sub_prompt,
                    "original_analysis": analysis_text,
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": self.model_id,
                        "total_extractions": 0,
                        "error": "Missing DATABRICKS_API_KEY"
                    }
                }

            # Ensure we use a Databricks model ID
            model_to_use = self.model_id if self.model_id.startswith("databricks") else "databricks-llama-4-maverick"

            try:
                result = lx.extract(
                    text_or_documents=input_text,
                    prompt_description=prompt,
                    examples=examples,
                    model_id=model_to_use,
                )
            except Exception as api_error:
                logger.error(f"Databricks extraction failed: {api_error}")
                return {
                    "sub_prompt": sub_prompt,
                    "original_analysis": analysis_text,
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": model_to_use,
                        "total_extractions": 0,
                        "error": str(api_error)
                    }
                }
            
            # Process the results
            if result and hasattr(result, 'extractions') and result.extractions:
                extracted_facts = {
                    "sub_prompt": sub_prompt,
                    "original_analysis": analysis_text,
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": self.model_id,
                        "total_extractions": len(result.extractions)
                    }
                }
                
                for extraction in result.extractions:
                    fact = {
                        "category": extraction.extraction_class,
                        "text": extraction.extraction_text,
                        "attributes": extraction.attributes or {}
                    }
                    extracted_facts["extracted_facts"].append(fact)
                
                logger.info(f"Successfully extracted {len(result.extractions)} facts")
                return extracted_facts
            else:
                logger.warning("No facts extracted from analysis text")
                return None
                
        except Exception as e:
            logger.error(f"Error during fact extraction: {e}", exc_info=True)
            return None
    
    def extract_facts_from_multiple_analyses(self, analyses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract facts from multiple analysis results.
        
        Args:
            analyses: List of analysis dictionaries containing analysis text and metadata
            
        Returns:
            List of dictionaries containing extracted facts for each analysis
        """
        results = []
        
        for analysis in analyses:
            try:
                analysis_text = analysis.get("analysis_summary", "")
                sub_prompt = analysis.get("sub_prompt_analyzed", "")
                context = analysis.get("analysis_context", "")
                
                if not analysis_text:
                    logger.warning(f"No analysis text found for sub-prompt: {sub_prompt}")
                    continue
                
                extracted_facts = self.extract_facts_from_analysis(
                    analysis_text=analysis_text,
                    sub_prompt=sub_prompt,
                    context=context
                )
                
                if extracted_facts:
                    # Add original analysis metadata
                    extracted_facts["original_analysis_metadata"] = {
                        "title": analysis.get("title", ""),
                        "supporting_quotes": analysis.get("supporting_quotes", [])
                    }
                    results.append(extracted_facts)
                    
            except Exception as e:
                logger.error(f"Error processing analysis for fact extraction: {e}")
                continue
        
        logger.info(f"Completed fact extraction for {len(results)} analyses")
        return results

    def extract_facts_from_text(self, text: str, context: str = "") -> Dict[str, Any]:
        """
        Extract facts from a single text (e.g., analysis section).

        Args:
            text: The text to extract facts from
            context: Additional context about the text

        Returns:
            Dictionary containing extracted facts and metadata
        """
        # Databricks-only: require API key
        if not os.environ.get('DATABRICKS_API_KEY'):
            logger.error("DATABRICKS_API_KEY not available; cannot perform fact extraction")
            return {
                "extracted_facts": [],
                "extraction_metadata": {
                    "model_used": self.model_id,
                    "total_extractions": 0,
                    "error": "Missing DATABRICKS_API_KEY",
                    "context": context
                }
            }

        try:
            import langextract as lx

            # Create extraction prompt
            extraction_prompt = f"""
            Extract structured facts from the following analysis text.

            Context: {context}

            Text to analyze:
            {text}

            Please extract key facts including:
            - Financial amounts, dates, percentages
            - Legal entities, parties, jurisdictions
            - Key terms, definitions, requirements
            - Important deadlines, conditions, obligations

            Format each fact with:
            - text: The factual statement
            - category: Type of fact (financial, legal, temporal, etc.)
            - attributes: Relevant details (amount, date, entity, etc.)
            """

            # Use structured examples compatible with LangExtract
            examples = [
                lx.data.ExampleData(
                    text="The investment amount is $50 million with a 15% annual return expected by December 2024.",
                    extractions=[
                        lx.data.Extraction(
                            extraction_class="financial",
                            extraction_text="$50 million",
                            attributes={"type": "investment"}
                        ),
                        lx.data.Extraction(
                            extraction_class="financial",
                            extraction_text="15%",
                            attributes={"type": "return", "frequency": "annual"}
                        ),
                        lx.data.Extraction(
                            extraction_class="temporal",
                            extraction_text="December 2024",
                            attributes={"type": "deadline"}
                        ),
                    ],
                )
            ]

            # Databricks-only extraction per workspace policy
            model_to_use = self.model_id if self.model_id.startswith("databricks") else "databricks-llama-4-maverick"
            try:
                result = lx.extract(
                    text_or_documents=extraction_prompt,
                    prompt_description="Extract structured facts from the analysis text",
                    examples=examples,
                    model_id=model_to_use,
                )
            except Exception as e:
                logger.error(f"Databricks extraction failed: {e}")
                return {
                    "extracted_facts": [],
                    "extraction_metadata": {
                        "model_used": model_to_use,
                        "total_extractions": 0,
                        "error": str(e),
                        "context": context
                    }
                }

            # Process and return results (normalize to our schema)
            facts_out = []
            if hasattr(result, 'extractions') and result.extractions:
                for ext in result.extractions:
                    facts_out.append({
                        "category": getattr(ext, "extraction_class", None),
                        "text": getattr(ext, "extraction_text", None),
                        "attributes": getattr(ext, "attributes", {}) or {}
                    })
            else:
                logger.warning("No facts extracted from text")

            return {
                "extracted_facts": facts_out,
                "extraction_metadata": {
                    "model_used": model_to_use,
                    "total_extractions": len(facts_out),
                    "context": context
                }
            }

        except Exception as e:
            logger.error(f"Error extracting facts from text: {e}", exc_info=True)
            return {
                "extracted_facts": [],
                "extraction_metadata": {
                    "model_used": self.model_id,
                    "total_extractions": 0,
                    "error": str(e),
                    "context": context
                }
            }

    def _generate_mock_facts(self, text: str, context: str = "") -> List[Dict[str, Any]]:
        """Generate mock facts for demonstration when API is not available."""
        import re

        mock_facts = []

        # Look for financial amounts
        money_pattern = r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion|thousand))?'
        money_matches = re.findall(money_pattern, text, re.IGNORECASE)
        for amount in money_matches[:3]:  # Limit to 3
            mock_facts.append({
                "text": f"Financial amount mentioned: {amount}",
                "category": "financial",
                "attributes": {"amount": amount, "type": "monetary_value"}
            })

        # Look for percentages
        percent_pattern = r'\d+(?:\.\d+)?%'
        percent_matches = re.findall(percent_pattern, text)
        for percent in percent_matches[:2]:  # Limit to 2
            mock_facts.append({
                "text": f"Percentage value: {percent}",
                "category": "financial",
                "attributes": {"percentage": percent, "type": "rate"}
            })

        # Look for dates
        date_pattern = r'(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}|\d{1,2}/\d{1,2}/\d{4}|\d{4}-\d{2}-\d{2}'
        date_matches = re.findall(date_pattern, text, re.IGNORECASE)
        for date in date_matches[:2]:  # Limit to 2
            mock_facts.append({
                "text": f"Date mentioned: {date}",
                "category": "temporal",
                "attributes": {"date": date, "type": "date_reference"}
            })

        # Look for key terms that might be important
        key_terms = ['loan', 'investment', 'contract', 'agreement', 'rate', 'term', 'maturity', 'principal', 'interest']
        for term in key_terms:
            if term.lower() in text.lower():
                mock_facts.append({
                    "text": f"Document contains reference to {term}",
                    "category": "general",
                    "attributes": {"term": term, "type": "key_concept"}
                })
                break  # Only add one general fact

        # If no specific patterns found, create a general fact
        if not mock_facts:
            mock_facts.append({
                "text": "Analysis contains structured information for review",
                "category": "general",
                "attributes": {"type": "content_summary", "length": len(text)}
            })

        return mock_facts
